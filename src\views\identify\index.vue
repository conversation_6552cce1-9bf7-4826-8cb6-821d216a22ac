<template>
  <div class="identify-page" :style="{ paddingTop: `${titleHeight}px`}">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="content-table-list"
                      :style="{ height: `calc(100vh - 10px - 49px - ${titleHeight}px)`}"
                      @scroll="handleScroll">
      <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="finishedText"
          @load="onLoad"
          scroller
          ref="listRef"
          :immediate-check="false"
          :error.sync="errorStatus"
          @touchmove="onTouchMove"
          @touchend="onTouchEnd"
      >
        <div class="header-container">
          <van-search v-model="searchText" readonly placeholder="搜索求鉴关键词…" shape="round"
                      @click-input="handleFilter(1)">
            <template #left-icon>
              <img class="search-icon" :src="searchIcon">
            </template>
            <template #right-icon>
              <img class="search-filter" :src="filterIcon" @click="handleFilter(1)">
            </template>
          </van-search>
          <img class="user-icon" :src="userMes?.train ? trainPeople : myIcon" @click="handleAttribute">
        </div>
        <!--        <div class="sticky-tabs">-->
        <!--          <Tabs :category="category" @change="handleTab"/>-->
        <!--        </div>-->
        <van-sticky :offset-top="`${titleHeight}`">
          <Tabs :category="category" @change="handleTab"/>
        </van-sticky>
        <div class="brand-container">
          <div class="brand-box" v-for="(item, index) in brandList" :key="index" @click="handleBrand(item)">
            <div v-if="index <= 2" class="brand-box">
              <img class="brand-icon" :src="item.logo">
            </div>
            <div v-if="index >= 3" class="brand-item">
              <img class="other-icon" :src="otherIcon">
            </div>
            <div class="brand-desc">
              <div class="desc-text">{{ `求${formatWan(item.count) || 0} / 待${formatWan(item.countWait) || 0}` }}</div>
            </div>
          </div>
        </div>
        <!--        <Plate v-if="briefStore.searchInfo.categoryKey === -1"/>-->
        <Plate v-if="categoryKey === -1"/>
        <van-skeleton v-if="skeletonLoading" title :loading="skeletonLoading" :row="25"/>
        <IdentifyList v-if="!skeletonLoading" ref="IdentifyListRef" :switchIcon="true" :state="stateKey"
                      :tableList="tableList"
                      @change="handleScreen" @sort="handleSort"/>
        <div v-if="loading !== true && !tableList.length">
          <Empty class="pt-50"/>
        </div>
        <AttributePopup ref="attributePopupRef"/>
        <ReleasePopup ref="releasePopupRef" @save="handleNextStep"/>
        <MustSeePopup ref="mustSeePopupRef" @close="closeMustPopup"/>
      </van-list>
    </van-pull-refresh>
    <TabBar :page="1" :page-index="true" :diffNew="diffNew" @backTop="backTop" @my-page="handleAttribute"/>
    <div class="posted">
      <view v-if="allBtn && !skeletonLoading" class="all-btn" @click="handleRelease">
        <img class="edit-icon" :src="editIcon"/>
        <text>求鉴</text>
      </view>
      <view v-else key="half-btn" class="half-btn">
        <img class="edit-icon" :src="editIcon"/>
      </view>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted, computed, nextTick, watch} from 'vue'
import {useRouter, useRoute} from "vue-router";
import commonApi from "@/services/common.js";
import {setCache, getCache, getCookie} from "@/utils/cache.js";
import Tabs from './components/tabs.vue'
import Plate from './components/plate.vue'
import TabBar from "@/components/tabbar.vue";
import IdentifyList from './components/identifyList.vue'
import AttributePopup from "@/views/identify/components/attributePopup.vue";
import ReleasePopup from "@/views/identify/components/releasePopup.vue";
import MustSeePopup from "@/views/identify/components/mustSeePopup.vue";
import shareIcon from "@/assets/images/common/shareIcon.png"
import searchIcon from "@/assets/images/identify/searchIcon.png"
import filterIcon from "@/assets/images/identify/filterIcon.png"
import trainPeople from "@/assets/images/identify/trainPeople.png"
import myIcon from "@/assets/images/identify/myIcon.png"
import otherIcon from "@/assets/images/identify/otherIcon.png"
import editIcon from "@/assets/images/common/editIcon.png"
import identifyApi from "@/services/identify.js";
import {useAuthStore} from "@/stores/auth.js";
import {isIOS, isUserGroup, jumpLogin, otherLogin, formatWan, jumpUser} from "@/utils/common.js";

const authStore = useAuthStore();
const router = useRouter() // 路由
const route = useRoute() // 路由


const userInfo = computed(() => {
  return authStore.userInfo
})

const titleHeight = document.getElementById('myTitle')?.offsetHeight || 0;
// const categoryKey = ref(-1)
const categoryKey = ref(route.query?.category ? Number(route.query?.category) : -1)
const stateKey = ref(0)
const brandKey = ref()
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const skeletonLoading = ref(true)
const allBtn = ref(true)
const listRef = ref()
const finishedText = ref('没有更多了')
const errorStatus = ref(false)
const tableList = ref([]);
// const category = ref(-1)
const category = ref(route.query?.category ? Number(route.query?.category) : -1)
const searchText = ref('')
const attributePopupRef = ref('')
const releasePopupRef = ref('')
const mustSeePopupRef = ref()
const userMes = ref()
const diffNew = ref(false)  //是否有异鉴
const params = ref({
  // uid: userInfo?.value && userInfo.value.id ? userInfo.value.id : null,
  page: 1,
  pageSize: 10,
})
const order = ref(1)
const red = ref(null)
const appraisal = ref(0)
let scrollTimeout; // 用于检测滚动是否停止

const brandList = ref([
  {
    id: 1,
    name: "尤尼克斯",
    ename: "YONEX",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/yonex.png"
  },
  {
    id: 22,
    name: "李宁",
    ename: "Lining",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/lining.png"
  },
  {
    id: 2,
    name: "威克多",
    ename: "VICTOR",
    logo: "https://www.badmintoncn.com/cbo_eq/brandLogo/victor.png"
  },
  {
    id: 0,
    name: "其他",
    ename: "VICTOR",
    logo: ""
  },
])

const handleFilter = (type) => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/identify/screen?mag_hide_progress=1&backFlag=false`, {
      brandKey: brandKey.value,
      stateKey: stateKey.value,
      categoryKey: categoryKey.value,
      type: type
    });
  } else {
    router.push({
      name: 'screen',
      query: {
        backFlag: false,
        type: type,
        brandKey: brandKey.value,
        stateKey: stateKey.value,
        categoryKey: categoryKey.value
      }
    })
  }
}

const handleBrand = (item) => {
  brandKey.value = item.id
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/brand/index?mag_hide_progress=1`, {
      brandKey: brandKey.value,
      brandLogo: item.logo,
      categoryKey: categoryKey.value
    });
  } else {
    router.push({
      name: 'brand',
      query: {
        brandKey: brandKey.value,
        brandLogo: item.logo,
        categoryKey: categoryKey.value
      }
    })
  }
}

const pageLoad = (type = 1) => {
  window.mag.setPageLife({
    pageAppear: async function () {
      params.value.page = 1;
      tableList.value = []
      if (authStore.token) {
        await getUserMes()
      } else {
        skeletonLoading.value = false
      }
      await getSearchList()
      if(type === 2) {
        await getDiffCount()
      }
    }
  });
}


const handleAttribute = () => {
  if (authStore.token) {
    if (authStore.phone) {
      const apiUrl = authStore.apiUrl
      window.mag.newWin(`${apiUrl}/myAppraisal/index?mag_hide_progress=1`);
      pageLoad()
    } else {
      router.push({
        name: 'myAppraisal'
      })
    }
  } else {
    // jumpUser()
    jumpLogin()
  }
}

const closeMustPopup = () => {
  releasePopupRef.value.showIndex()
}

const getUserMes = async () => {
  let res = await identifyApi.queryUserList({ids: userInfo.value.id})
  if (res.code === 200) {
    let list = res.data?.list[0]
    userMes.value = list
    skeletonLoading.value = false
    setCache('USER_MESSAGE', JSON.stringify(list))
  }
}

const getSetting = async () => {
  let res = await identifyApi.getSetting({uid: userInfo.value.id})
  if (res.code === 200) {
    return res.data.list
  }
}

const handleRelease = async () => {
  if (authStore.phone) {
    if (authStore.token) {
      let res = await getSetting()
      if (res.countThread >= 3) {
        closeMustPopup()
      } else {
        mustSeePopupRef.value.show()
      }
    } else {
      // jumpUser()
      jumpLogin()
    }
  } else {
    alert('请到中羽在线APP中发布')
  }
}

const handleScreen = (val) => {
  appraisal.value = val
  params.value.page = 1;
  stateKey.value = val
  tableList.value = []
  getSearchList()
}

const handleSort = (val) => {
  if(val === 4) {
    order.value = 1
    red.value = true
  } else {
    red.value = null
    order.value = val
  }
  tableList.value = []
  params.value.page = 1;
  getSearchList()
}

const handleNextStep = (item) => {
  if (authStore.phone) {
    const apiUrl = authStore.apiUrl
    window.mag.newWin(`${apiUrl}/release/index?mag_hide_progress=1&needlogin=1`, {
      brandId: item.brandId,
      categoryId: item.categoryId,
      // logo: item.logo
    })
  } else {
    router.push({
      name: 'release',
      query: {
        brandId: item.brandId,
        categoryId: item.categoryId,
        // logo: item.logo
      }
    })
  }
}


const backTop = () => {
  const pullRefreshEl = document.querySelector('.content-table-list');
  if (pullRefreshEl) {
    pullRefreshEl.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }
}

const handleTab = (item) => {
  category.value = item.name
  categoryKey.value = category.value
  tableList.value = []
  params.value.page = 1;
  getSearchList()
}

const onTouchMove = (event) => {
  allBtn.value = false
}

const onTouchEnd = () => {
  allBtn.value = true
}

const handleScroll = (scroll) => {
  clearTimeout(scrollTimeout);
  allBtn.value = false; // 滚动时设置为 false
  scrollTimeout = setTimeout(() => {
    allBtn.value = true; // 停止滚动后设置为 true
  }, 200); // 短暂延迟以确保滚动完全停止
}

const onRefresh = () => {
  params.value.page = 1;
  refreshing.value = true
  nextTick(() => {
    getSearchList();
  })
}

const onLoad = () => {
  if (tableList.value) {
    params.value.page++
    nextTick(() => {
      getSearchList();
    })
  }
};


const getSearchList = () => {
  let paramsCopy = {
    ...params.value,
    keyword: searchText.value,
    brand: null,
    cate: categoryKey.value === -1 ? null : categoryKey.value,
    appraisal: appraisal.value === 0 ? null : appraisal.value,
    order: order.value,
    red: red.value ? red.value : null,
  };
  loading.value = true
  // return;
  identifyApi.getSearch(paramsCopy).then((res) => {
    if (res.code === 200) {
      if (refreshing.value) {
        tableList.value = [];
        refreshing.value = false;
        finished.value = false;
      }
      let data = res.data || {};
      let dataList = data.list || [];
      let records = dataList || [];
      tableList.value = tableList.value.concat(records);
      let total = data.total || 0;
      if (total <= tableList.value.length || res.data.list === null) {
        finished.value = true;
      } else {
        finished.value = false;
      }
      finishedText.value = "没有更多了";
      if (tableList.value.length === 0) {
        finishedText.value = "";
      }
    } else if (res.code === 502) {
      errorStatus.value = true;
      if (authStore.phone) {
        window.mag.closeWin();
        const apiUrl = authStore.apiUrl
        window.mag.newWin(`${apiUrl}/upgrade?mag_hide_progress=1`);
      } else {
        router.push({
          name: 'upgrade'
        })
      }
    } else {
      errorStatus.value = true;
    }
  }).finally(() => {
    skeletonLoading.value = false
    loading.value = false
  })
}

const getBrandCount = async () => {
  let res = await identifyApi.getThreadCount({
    uid: null,
    type: 1,
    target: 'brand'
  })
  if (res?.code === 200) {
    let list = []
    list = res.data.list
    if (list.length) {
      setCache('BRAND_LIST', JSON.stringify(list))
      brandList.value = brandList.value.map((brand) => {
        const matchedCount = list.find((item) => item.id === brand.id);
        return {
          ...brand,
          count: matchedCount ? matchedCount.count : 0,
          countWait: matchedCount ? matchedCount.countWait : 0,
        };
      });
    }
  }
}


const getDiffCount = async () => {
  let res = await identifyApi.getThreadCount({
    uid: userInfo.value.id,
    type: 2,
    target: 'others',
  })
  if (res.code === 200) {
    if (res?.data?.list?.length) {
      diffNew.value = res.data.list[1]?.notice
    }
  }
}


// const getCategoryCount = async () => {
//   let res = await identifyApi.getThreadCount({
//     uid: userInfo.value.id,
//     type: 1,
//     target: 'cate'
//   })
//   if (res.code === 200) {
//     let list = res.data.list
//     if (list.length) {
//       setCache('CATEGORY_LIST', JSON.stringify(list))
//     }
//   }
// }

const webLogin = async (token) => {
  const userAgent = navigator.userAgent || ''; // 获取 User-Agent
  let res = await identifyApi.tokenLogin({
    token: token,
    from: 1
    // from: 3
  })
  if (res.code === 200) {
    let data = res.data.list
    authStore.token = data.jwtToken.accessToken
    authStore.userInfo = {
      id: data.uid,
      // id: 117229,
      name: data.name,
      sex: data.sex,
      avatar: data.avatar,
      groupId: data.groupId,
      // groupId: 8,
    }
  }
}

// 添加一个标志来跟踪是否是初始加载
const isInitialLoad = ref(true)

watch(() => authStore.token,
    async () => {
      if (authStore.token) {
        skeletonLoading.value = true
        await getUserMes()
        if (!getCache('PROPERTIES')) {
          attributePopupRef.value.show()
        }
        // 如果是初始加载且登录成功，则调用 getSearchList
        if (isInitialLoad.value) {
          getSearchList()
          isInitialLoad.value = false
        }
      }
    })


onMounted(async () => {
  authStore.token = ''
  authStore.userInfo = {}
  await getBrandCount()
  // await getCategoryCount()
  // if (!getCache('CATEGORY_LIST')) {
  //   await getCategoryCount()
  // }
  authStore.phone = window.navigator.appVersion.toLowerCase().indexOf('magappx') != -1
  authStore.IS_IOS = isIOS()
  // authStore.apiUrl = 'https://www.badmintoncn.com/cbo_ea/dist/index.html#'
  // authStore.apiUrl = 'https://www.badmintoncn.com/cbo_ea_dev/dist/index.html#'
  authStore.apiUrl = 'http://192.168.31.146:8000/cbo_ea_dev/dist/index.html#'
  // authStore.apiUrl = 'http://192.168.31.146:8000/cbo_ea/dist/index.html#'
  // authStore.apiUrl = 'https://www.badmintoncn.com/cbo_ea/dist'
  // authStore.apiUrl = 'https://www.badmintoncn.com/cbo_ea_dev/dist#'
  // authStore.apiUrl = 'http://192.168.31.146:8000/cbo_ea_dev/dist#'
  // authStore.apiUrl = 'http://192.168.31.146:8000/cbo_ea/dist'
  if (authStore.phone) {
    window.mag.setTitle('装备鉴定');
  } else {
    document.title = '装备鉴定'
  }
  window.mag.showNavigation()
  if (!authStore.phone) {
    await webLogin('477378fae712011e34f2f54fac028f97')  //YOLOSO
    // await webLogin('f7cf086ba6176c5ab245ebc5c3ce829e')  //测试1023
    // await webLogin('673b9a01993a42e35ba38b784d7cae79')  //创造神话
    // await webLogin('69513ca96a863292d9398ea8d609c68a')  //创造神话小号
    // await webLogin('bd1613d168ed100a3f75202e2e5f5d06')  //飞羽裳-正式环境
    // await webLogin('477378fae712011e34f2f54fac028f97')  //YOLOSO-正式环境
    // await webLogin('2c2dl+J2tA378A+7lVa6T1dwxeLMngJJsy1ghb2WcvrkT3HaswC1b0OTYBEi')  //YOLOSO-正式环境
    // await webLogin('eece7grFN40CFmq%2F9bjbk9Vf1%2FgN3j4hOH7VZPHsskM3T9OGD6SvLp2hWcY8')  //创造神话-正式环境
  }
  //这个只有测试环境才放开 因为测试环境拿不到马甲的token
  // await otherLogin()
  if (authStore.phone) {
    if (getCookie('cbo_magapp_token')?.length > 1) {
      await otherLogin()
    }
  } else {
    await otherLogin()
  }

  // 如果没有登录成功（没有token），则直接调用 getSearchList
  if (!authStore.token) {
    getSearchList()
    isInitialLoad.value = false
  }

  await getDiffCount()

  // setTimeout(async () => {
  //   if (authStore.token) {
  //     skeletonLoading.value = true
  //    await getUserMes()
  //     if (!getCache('PROPERTIES')) {
  //       attributePopupRef.value.show()
  //     }
  //   } else {
  //     skeletonLoading.value = false
  //   }
  //  await getSearchList()
  // }, 500)
  if (!getCache('PERMISSION') && authStore.userInfo.id) {
    let config = await commonApi.getConfig({
      id: authStore.userInfo.id
    })
    if (config.code === 200) {
      setCache("PERMISSION", JSON.stringify(config.data.list))
    }
  }
  await isUserGroup()
})

const handleVisibilityChange = () => {
  if (document.hidden) {
    pageLoad(2)
  }
};


onMounted(() => {
  document.addEventListener('visibilitychange', handleVisibilityChange);
  nextTick(() => {
    const scrollContainer = listRef.value?.$el?.querySelector('.van-list');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
    }
  });
  window.mag.setData({
    shareData: {
      title: '装备鉴定',
      des: '羽毛球器材鉴定，羽毛球装备鉴定，真假鉴别',
      picurl: 'https://www.badmintoncn.com/cbo_ea/img/share.png',
      linkurl: window.location.href,
    }
  });
});



// 在组件卸载前移除滚动事件
onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  const scrollContainer = listRef.value?.$el?.querySelector('.van-list');
  if (scrollContainer) {
    scrollContainer.removeEventListener('scroll', handleScroll);
  }
});


</script>

<style scoped lang="scss">
.identify-page {
  width: 100vw;
  //height: 100vh;
  height: 100%;
  overflow: hidden;
  background-color: #FFFFFF;

  .header-container {
    display: flex;
    align-items: center;
    padding: 10px 12px 12px;

    .van-search {
      width: 90%;
      padding: 0;
      --van-search-input-height: 36px
    }

    .search-icon {
      margin-top: 6px;
      height: 14px;
      width: 14px;
    }

    .search-filter {
      width: 14px;
      height: 12px;
      margin-top: 7px;
      margin-right: 3px;
    }

    .user-icon {
      width: 22px;
      height: 22px;
      padding: 0 0 4px 12px;
    }
  }

  .content-table-list {
    //overflow: auto;
    overflow-y: auto;
    scrollbar-width: none;
  }

  .content-table-list::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }

  .sticky-tabs {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 10;
  }

  .brand-container {
    padding: 12px;
    display: flex;
    justify-content: space-between;

    .brand-box {
      background-color: #F5F5F5;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;

      .brand-item {
        width: 84px;
        height: 47px;
        background: #478E87;
        border-radius: 5px;
        text-align: center;

        .other-icon {
          padding-top: 6px;
          width: 66px;
          height: 35px;
        }
      }

      .brand-box {
        width: 84px;
        height: 47px;

        .brand-icon {
          width: 84px;
          height: 47px;
          border-radius: 5px;
        }
      }

      .brand-desc {
        text-align: center;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;

        .desc-text {
          padding: 7px 5px;
          font-size: 10px;
          font-weight: 400;
          color: #666666;
        }
      }
    }
  }

  .posted {
    position: absolute;
    right: 0;
    top: 75%;
    z-index: 1;

    .edit-icon {
      width: 20px;
      height: 20px;
      margin-right: 2px;
    }

    .all-btn {
      width: 76px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #FFFFFF;
      font-size: 14px;
      margin-right: 12px;
      background-color: #000000;
      border-radius: 30px;
    }

    .half-btn {
      width: 44px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #FFFFFF;
      font-size: 14px;
      background-color: #000000;
      border-radius: 20px 0 0 20px;
    }
  }
}
</style>